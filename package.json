{"name": "upload-service", "version": "1.0.0", "description": "", "main": "build/index.js", "scripts": {"start": "nodemon --watch \"./**\"  --ext \"ts,json\" --exec \"ts-node index.ts\"", "setup:prod": "npm install && npm run build", "setup:dev": "npm install", "build": "tsc"}, "author": "", "license": "ISC", "dependencies": {"@fastify/cors": "^9.0.1", "@fastify/multipart": "^7.6.0", "@fastify/rate-limit": "^9.1.0", "aws-sdk": "^2.1645.0", "axios": "^1.6.6", "dotenv": "^12.0.4", "fastify": "^4.25.2", "helocore": "^1.0.22", "minio": "^7.1.0", "nanoid": "^3.1.32", "pino": "^8.11.0", "reflect-metadata": "^0.1.13", "zod": "^3.21.4"}, "devDependencies": {"@prisma/client": "^4.2.1", "@types/dotenv": "^8.2.0", "@types/minio": "^7.0.18", "@types/node": "^24.3.0", "nodemon": "^2.0.15", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}
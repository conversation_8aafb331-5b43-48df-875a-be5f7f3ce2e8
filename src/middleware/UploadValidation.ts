import { z } from 'zod'
import enums from '../libs/enums'
import { Body, defineMiddleware, File, Query } from 'helocore'
import { TFile } from '../types/upload'

@defineMiddleware
export default class UploadValidation {
  constructor() { }

  async FilterMimeType(@File('file') file: Array<TFile>, @Query query: { force: string }) {
    if (file[0].limit) {
      throw new Error('Media Size Big.')
    }

    if (!file[0].data) {
      throw new Error('Media Data is not Found.')
    }

    if (query.force === 'true') {
      return
    }

    const isType = [
      ...enums.allowed_document_types,
      ...enums.allowed_image_types,
      ...enums.allowed_video_types,
      ...enums.allowed_voice_types,
      ...enums.allowed_application_types
    ].find(a => a === file[0].mimetype)
    if (!isType) {
      throw new Error('Media Type Not Supported')
    }

    if (enums.allowed_image_types.includes(file[0].mimetype)) {
      if (file[0].data.length >= 5000000) {
        throw new Error('Media Size Big. Limit is ' + 5000000 + ' byte')
      }
    }

    if (enums.allowed_video_types.includes(file[0].mimetype)) {
      if (file[0].data.length >= 16000000) {
        throw new Error('Media Size Big. Limit is ' + 16000000 + ' byte')
      }
    }

    if (enums.allowed_voice_types.includes(file[0].mimetype)) {
      if (file[0].data.length >= 16000000) {
        throw new Error('Media Size Big. Limit is ' + 16000000 + ' byte')
      }
    }

    if (enums.allowed_document_types.includes(file[0].mimetype)) {
      if (file[0].data.length >= 100000000) {
        throw new Error('Media Size Big. Limit is ' + 100000000 + ' byte')
      }
    }
  }

  async FilterMimeTypeForAdcreativeImage(@File('file') file: Array<TFile>, @Query query: { force: string }) {
    if (file[0].limit) {
      throw new Error('Media Size Big.')
    }

    if (!file[0].data) {
      throw new Error('Media Data is not Found.')
    }

    if (query.force === 'true') {
      return
    }

    const isType = [
      ...enums.allowed_image_types,
    ].find(a => a === file[0].mimetype)
    if (!isType) {
      throw new Error('Media Type Not Supported')
    }

    if (enums.allowed_image_types.includes(file[0].mimetype)) {
      if (file[0].data.length >= 5000000) {
        throw new Error('Media Size Big. Limit is ' + 5000000 + ' byte')
      }
    }
  }

  async FilterMimeTypeForAdcreativeVideo(@File('file') file: Array<TFile>, @Query query: { force: string }) {
    if (file[0].limit) {
      throw new Error('Media Size Big.')
    }

    if (!file[0].data) {
      throw new Error('Media Data is not Found.')
    }

    if (query.force === 'true') {
      return
    }

    const isType = [
      ...enums.allowed_video_types,
    ].find(a => a === file[0].mimetype)
    if (!isType) {
      throw new Error('Media Type Not Supported')
    }

    // if (enums.allowed_video_types.includes(file[0].mimetype)) {
    //   if (file[0].data.length >= 16000000) {
    //     throw new Error('Media Size Big. Limit is ' + 16000000 + ' byte')
    //   }
    // }

  }

  async RemoveFile(@Body body: { file_name: string }) {
    const schema = z.object({
      file_name: z.string({ required_error: 'Cannnot Empty file_name field', invalid_type_error: 'file_name field must be string' }).nonempty({ message: 'Cannnot Empty file_name field' }),
      bucket_name: z.string({ required_error: 'Cannnot Empty bucket_name field', invalid_type_error: 'bucket_name field must be string' }).optional()
    })

    await schema.parseAsync(body)
  }
}

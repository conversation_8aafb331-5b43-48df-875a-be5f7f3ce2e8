import { injectable } from 'helocore'
import axios, { AxiosResponse } from 'axios';
import config from '../config'
import { AdImageResponse } from '../types/facebook';
import FormData from 'form-data';


@injectable()
export default class FacebookService {
  url = 'https://graph.facebook.com/v18.0/'

  constructor() { }

  async CreateSession(mimetype: string, size: number, fileName: string): Promise<string> {
    const requestConfig = {
      url: this.url + `${config.FACEBOOK_APP_ID}/uploads`,
      method: 'POST',
      params: {
        access_token: config.FACEBOOK_APP_TOKEN,
        file_type: mimetype,
        file_length: size,
        file_name: fileName
      }
    }

    return await axios.request(requestConfig).then(response => response.data.id)
  }

  async UploadFile(endpoint: string, buffer: Buffer): Promise<string> {
    const requestConfig = {
      url: this.url + endpoint,
      method: 'POST',
      headers: {
        'Authorization': `OAuth ${process.env.FACEBOOK_APP_TOKEN}`,
        'file_offset': '0'
      },
      data: buffer
    }

    return await axios.request(requestConfig).then(response => response.data.h).catch(err => {
      console.log(err)
    })
  }

  async UploadAdcreativeImage(adaccountId:string,bytes:string,childBmUserAccessToken:string): Promise<AxiosResponse<AdImageResponse>> {

    const formData = new FormData();
    formData.append('bytes', bytes);
    const requestConfig = {
      url: `https://graph.facebook.com/v23.0/act_${adaccountId}/adimages`,
      method: 'POST',
      maxBodyLength: Infinity,
      headers: {
        ...formData.getHeaders()
      },
      params: {
        access_token: childBmUserAccessToken,
      },
      data: formData
    }
    return await axios.request(requestConfig)
  }

  async UploadAdcreativeVideo(adaccountId:string,buffer:Buffer,childBmUserAccessToken:string): Promise<string> {
    const formData = new FormData();
    formData.append('video_file_chunk', buffer);
    const requestConfig = {
      url: `https://graph.facebook.com/v23.0/act_${adaccountId}/advideos`,
      method: 'POST',
      maxBodyLength: Infinity,
      headers: {
        ...formData.getHeaders()
      },
      params: {
        access_token: childBmUserAccessToken,
      },
      data: formData
    }
    return await axios.request(requestConfig).then(response => response.data.id)
  }
}

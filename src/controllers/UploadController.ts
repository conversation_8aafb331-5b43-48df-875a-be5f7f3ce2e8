import { Body, Controller, Delete, File, Get, Middleware, Post, Query, injectable,Request } from 'helocore'
import ServiceResponse from '../models/ServiceResponse'
import IUploadController from '../interfaces/IUploadController'
import { TFile } from '../types/upload'
import UploadService from '../services/UploadService'
import UploadValidation from '../middleware/UploadValidation'
import AWSS3Service from '../services/AWSS3Service'
import { TraceId } from '../modules/CustomDecorator'
import CloudflareR2Service from '../services/CloudflareR2Service'
import helpers from "../libs/helpers";

@Controller('/file')
@injectable()
export default class UploadController implements IUploadController {
  constructor(
    private readonly uploadService: UploadService,
    private readonly awsS3Service: AWSS3Service,
    private readonly cloudflareR2Service: CloudflareR2Service,
  ) { }

  @Post('/upload')
  @Middleware([{ funcs: ['FilterMimeType'], class: UploadValidation }])
  // @RateLimit({
  //   max: 10,
  //   timeWindow: 10000
  // })
  async FileUpload(@File('file') file: Array<TFile>, @Query query: { bucket_name: string }): Promise<ServiceResponse<any>> {
    const url = await this.uploadService.PutFile(file[0], query.bucket_name)

    const serviceResponse = new ServiceResponse<any>()
    serviceResponse.data = {
      url: url
    }
    return serviceResponse
  }

  @Delete('/')
  @Middleware([{ funcs: ['RemoveFile'], class: UploadValidation }])
  // @RateLimit({
  //   max: 3,
  //   timeWindow: 10000
  // })
  async RemoveFile(@Body body: { file_name: string, bucket_name: string }): Promise<ServiceResponse<any>> {
    const url = await this.uploadService.RemoveFile(body.file_name, body.bucket_name)

    const serviceResponse = new ServiceResponse<any>()
    serviceResponse.data = {
      url: url
    }
    return serviceResponse
  }

  @Post('/fb/upload')
  @Middleware([{ funcs: ['FilterMimeType'], class: UploadValidation }])
  // @RateLimit({
  //   max: 3,
  //   timeWindow: 10000
  // })
  async FBFileUpload(@File('file') file: Array<TFile>): Promise<ServiceResponse<any>> {
    const fileHandleString = await this.uploadService.FBUpload(file[0])

    const serviceResponse = new ServiceResponse<any>()
    serviceResponse.data = {
      file_handle: fileHandleString
    }
    return serviceResponse
  }

  @Post('/meta/ads/upload-image') // bu şekilde yapıldı çünkü facebook => messengera kastediyor.
  @Middleware([{ funcs: ['FilterMimeTypeForAdcreativeImage'], class: UploadValidation }])
  async MetaUploadAdcreativeImage(@File('file') file: Array<TFile>, @Request req: any, @Body body: {
    child_bm_id: string
  }): Promise<ServiceResponse<any>> {
    const token = helpers.tokenExtractor(req.headers.authorization)
    const childBmId = req.body.child_bm_id
    if (!childBmId) {
      const serviceResponse = new ServiceResponse<any>()
      serviceResponse.message = 'child_bm_id is required'
      serviceResponse.status_code = 400
      return serviceResponse
    }

    const uploadResponse = await this.uploadService.MetaUploadAdcreativeImage(token, childBmId, file[0])

    const serviceResponse = new ServiceResponse<any>()
    serviceResponse.data = {
      url: uploadResponse.data.images.bytes.url,
      hash: uploadResponse.data.images.bytes.hash
    }
    return serviceResponse
  }

  @Post('/meta/ads/upload-video') // bu şekilde yapıldı çünkü facebook => messengera kastediyor.
  @Middleware([{ funcs: ['FilterMimeTypeForAdcreativeVideo'], class: UploadValidation }])
  async MetaUploadAdcreativeVideo(@File('file') file: Array<TFile>, @Request req: any, @Body body: {
    child_bm_id: string
  }): Promise<ServiceResponse<any>> {
    const token = helpers.tokenExtractor(req.headers.authorization)
    const childBmId = req.body.child_bm_id
    if (!childBmId) {
      const serviceResponse = new ServiceResponse<any>()
      serviceResponse.message = 'child_bm_id is required'
      serviceResponse.status_code = 400
      return serviceResponse
    }

    const videoId = await this.uploadService.MetaUploadAdcreativeVideo(token, childBmId, file[0])

    const serviceResponse = new ServiceResponse<any>()
    serviceResponse.data = {
      video_id: videoId
    }
    return serviceResponse

  }

  @Get('/aws/url')
  async AwsGetUrl(@Query query: { bucket_name: string, file_name: string }, @TraceId traceId: string): Promise<ServiceResponse<any>> {
    const url = await this.awsS3Service.getPresignedUrl(query.file_name, traceId, query.bucket_name)

    const serviceResponse = new ServiceResponse<any>()
    serviceResponse.data = {
      url: url
    }
    return serviceResponse
  }

  @Get('/cloudflare/url')
  async CloudflareGetUrl(@Query query: { bucket_name: string, file_name: string }, @TraceId traceId: string): Promise<ServiceResponse<any>> {
    const { url, object_url, public_url } = await this.cloudflareR2Service.getPresignedUrl(query.file_name, traceId, query.bucket_name)

    const serviceResponse = new ServiceResponse<any>()
    serviceResponse.data = {
      url: url,
      object_url: object_url,
      public_url: public_url
    }
    return serviceResponse
  }
}
